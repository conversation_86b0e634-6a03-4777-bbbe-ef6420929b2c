defmodule MqttableWeb.UnifiedPayloadEditorComponentTest do
  use MqttableWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias MqttableWeb.UnifiedPayloadEditorComponent

  describe "file upload functionality" do
    test "handles file_uploaded event correctly" do
      # Create test data
      test_filename = "test.txt"
      test_content = "Hello, World! This is a test file."
      test_base64 = Base.encode64(test_content)
      test_size = byte_size(test_content)
      test_type = "text/plain"

      # Mount the component
      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          payload: "",
          payload_format: "file",
          uploaded_file: nil,
          file_upload_error: nil,
          payload_text: "",
          payload_json: "",
          payload_hex: "",
          payload_file: "",
          current_payload: "",
          __changed__: %{}
        }
      }

      # Simulate file upload event
      params = %{
        "filename" => test_filename,
        "content" => test_base64,
        "size" => test_size,
        "type" => test_type
      }

      # Call the handle_event function
      {:noreply, updated_socket} =
        UnifiedPayloadEditorComponent.handle_event("file_uploaded", params, socket)

      # Verify the file was processed correctly
      assert updated_socket.assigns.uploaded_file != nil
      assert updated_socket.assigns.uploaded_file.filename == test_filename
      assert updated_socket.assigns.uploaded_file.size == test_size
      assert updated_socket.assigns.uploaded_file.type == test_type
      assert updated_socket.assigns.file_upload_error == nil

      # Verify the payload was set to the stored filename
      assert String.contains?(updated_socket.assigns.current_payload, "test_")
      assert String.contains?(updated_socket.assigns.payload_file, "test_")
    end

    test "handles file upload error for oversized file" do
      # Create test data that exceeds the 16MB limit
      # 17MB
      large_content = String.duplicate("x", 17 * 1024 * 1024)
      test_base64 = Base.encode64(large_content)

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          payload: "",
          payload_format: "file",
          uploaded_file: nil,
          file_upload_error: nil,
          payload_text: "",
          payload_json: "",
          payload_hex: "",
          payload_file: "",
          current_payload: "",
          __changed__: %{}
        }
      }

      params = %{
        "filename" => "large_file.txt",
        "content" => test_base64,
        "size" => byte_size(large_content),
        "type" => "text/plain"
      }

      # Call the handle_event function
      {:noreply, updated_socket} =
        UnifiedPayloadEditorComponent.handle_event("file_uploaded", params, socket)

      # Verify the error was handled correctly
      assert updated_socket.assigns.uploaded_file == nil
      assert updated_socket.assigns.file_upload_error != nil
      assert String.contains?(updated_socket.assigns.file_upload_error, "File too large")
    end

    test "handles invalid base64 content" do
      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          payload: "",
          payload_format: "file",
          uploaded_file: nil,
          file_upload_error: nil,
          payload_text: "",
          payload_json: "",
          payload_hex: "",
          payload_file: "",
          current_payload: "",
          __changed__: %{}
        }
      }

      params = %{
        "filename" => "test.txt",
        "content" => "invalid_base64_content!@#$%",
        "size" => 100,
        "type" => "text/plain"
      }

      # Call the handle_event function
      {:noreply, updated_socket} =
        UnifiedPayloadEditorComponent.handle_event("file_uploaded", params, socket)

      # Verify the error was handled correctly
      assert updated_socket.assigns.uploaded_file == nil
      assert updated_socket.assigns.file_upload_error != nil
      assert String.contains?(updated_socket.assigns.file_upload_error, "Failed to process file")
    end
  end
end
